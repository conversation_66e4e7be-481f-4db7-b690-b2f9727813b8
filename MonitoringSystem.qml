import QtQuick 2.12
import QtQuick.Controls 2.5
import QtQuick.Layouts 1.12
import QtCharts 2.3

Page {
    id: monitoringSystem
    
    // 信号定义
    signal navigateBack()

    // 分解炉选择按钮组
    ButtonGroup {
        id: boilerButtonGroup
        exclusive: true
    }

    // 返回首页状态标志
    property bool isReturningHome: false

    // 历史数据查看相关属性
    property bool isShowingHistorical: false
    property string currentHistoricalDate: ""
    property var currentHistoricalData: []  // 存储当前的历史数据
    property var historicalTableData: []    // 存储历史数据的表格格式

    // 添加回可能被引用的属性（简化版本，避免程序崩溃）
    property bool isBoilerSwitching: false
    property string pendingBoilerSwitch: ""
    property bool isZoomSwitching: false
    property string selectedBoilerUI: ""

    // 添加回可能被引用的其他属性
    property double scrollOffset: 0.0
    property bool userScrolling: false

    // 移除复杂的监控机制，保持简洁

    // 移除定时器 - 完全依赖C++信号推送机制
    // Redis可靠，每4次采集更新一次，C++发射信号，QML响应更新
    // 时间范围切换通过本地缩放实现，无需重新请求数据

    // 添加回一些基本定时器以避免程序崩溃（简化版本）
    Timer {
        id: scrollUpdateTimer
        interval: 100
        repeat: false
        onTriggered: {
            // 简化的滚动更新
            updateChartWithScroll()
        }
    }

    Timer {
        id: userScrollResetTimer
        interval: 3000
        repeat: false
        onTriggered: {
            // 简化的用户滚动重置
            if (smokeChart.userScrolling) {
                smokeChart.userScrolling = false
                updateScrollbarState()
            }
        }
    }

    // 移除防抖定时器，保持简洁

    // 简化的图表数据更新函数
    function updateChartData() {
        if (monitorWindow.dataSource.currentBoiler !== "") {
            console.log("开始更新图表数据 - 锅炉:", monitorWindow.dataSource.currentBoiler)
            // 直接调用数据源的更新方法
            monitorWindow.dataSource.updateSmokeChartSeriesWithMinutes(
                smokeO2Series, smokeCOSeries
            )
            console.log("图表数据更新调用完成")
        } else {
            console.log("无法更新图表：当前锅炉为空")
        }
    }

    // 设置深蓝色背景
    background: Rectangle {
        color: "#0E2250"
    }

    header: ToolBar {
        RowLayout {
            anchors.fill: parent
            Button {
                text: "返回首页"
                enabled: !isReturningHome
                onClicked: {
                    if (isReturningHome) return

                    isReturningHome = true

                    // 直接触发页面切换
                    stackView.pop()

                    // 简单的清理操作
                    Qt.callLater(function() {
                        performQuickCleanup()
                    })
                }
            }
            Label {
                text: "锅炉燃烧数据监控系统"
                font.pixelSize: 20
                elide: Label.ElideRight
                horizontalAlignment: Qt.AlignHCenter
                verticalAlignment: Qt.AlignVCenter
                Layout.fillWidth: true
            }

            Button {
                text: "采集配置"
                onClicked: {
                    stackView.push(configPage)
                }
                background: Rectangle {
                    color: parent.pressed ? "#1976d2" : "#2196f3"
                    radius: 4
                }
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    font.pixelSize: 16
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }

            Button {
                text: monitorWindow.dataSource.isRunning ? "停止监控" : "开始监控"
                onClicked: {
                    if (monitorWindow.dataSource.isRunning) {
                        monitorWindow.stopMonitoring()
                    } else {
                        monitorWindow.startMonitoring()
                    }
                }
                background: Rectangle {
                    color: monitorWindow.dataSource.isRunning ? "#f44336" : "#4caf50"
                    radius: 4
                }
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    font.pixelSize: 16
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Button {
                text: "清空数据"
                onClicked: monitorWindow.clearAllData()
                background: Rectangle {
                    color: parent.pressed ? "#ff9800" : "#ffc107"
                    radius: 4
                }
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    font.pixelSize: 16
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
        }
    }



    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 20
        spacing: 20

        // 数据连接状态指示区域 - 已注释
        /*
        Rectangle {
            Layout.fillWidth: true
            height: 80
            color: monitorWindow.dataSource.isDataConnected ? "#e8f5e8" : "#ffebee"
            radius: 12
            border.color: monitorWindow.dataSource.isDataConnected ? "#4caf50" : "#f44336"
            border.width: 2

            RowLayout {
                anchors.fill: parent
                anchors.margins: 20
                spacing: 30

                Rectangle {
                    width: 16
                    height: 16
                    radius: 8
                    color: monitorWindow.dataSource.isDataConnected ? "#4caf50" : "#f44336"
                }

                Label {
                    text: monitorWindow.dataSource.connectionStatus
                    font.pixelSize: 18
                    font.bold: true
                    color: "#333333"
                    Layout.fillWidth: true
                }

                Label {
                    text: {
                        if (!monitorWindow.dataSource.isRunning) {
                            return "监控已停止"
                        } else {
                            // 显示当前锅炉的实际采集间隔配置
                            var currentBoiler = monitorWindow.dataSource.currentBoiler
                            if (currentBoiler && currentBoiler !== "") {
                                return "数据采集间隔: 根据配置文件 (" + currentBoiler + ")"
                            } else {
                                return "数据采集间隔: 根据配置文件"
                            }
                        }
                    }
                    font.pixelSize: 16
                    color: "#666666"
                }
            }
        }
        */

        // 主要内容区域 - 水平布局
        RowLayout {
            Layout.fillWidth: true
            Layout.fillHeight: true
            spacing: 20



            // 烟气数据区域 - 现在占据全宽
            ScrollView {
                Layout.fillWidth: true
                Layout.fillHeight: true

                // 限制外层滚动方向，只允许垂直滚动
                ScrollBar.horizontal.policy: ScrollBar.AlwaysOff
                ScrollBar.vertical.policy: ScrollBar.AsNeeded

                // 设置滚动行为
                contentWidth: -1  // 禁用水平滚动
                clip: true

                ColumnLayout {
                    width: parent.width
                    spacing: 20

                    // 锅炉选择区域 - 已注释
                    /*
                    Rectangle {
                        Layout.fillWidth: true
                        height: 80
                        color: "#ffffff"
                        radius: 12
                        border.color: "#e0e0e0"
                        border.width: 1

                        RowLayout {
                            anchors.fill: parent
                            anchors.margins: 15
                            spacing: 20

                            Label {
                                text: "选择锅炉:"
                                font.pixelSize: 16
                                font.bold: true
                                color: "#333333"
                            }

                            // 动态生成锅炉按钮
                            Repeater {
                                model: monitorWindow.dataSource.boilerList

                                Button {
                                    text: modelData
                                    Layout.preferredWidth: 100
                                    Layout.preferredHeight: 40
                                    checkable: true
                                    // 使用本地UI状态来控制选中状态，提供即时反馈
                                    checked: selectedBoilerUI === modelData
                                    ButtonGroup.group: boilerButtonGroup

                                    background: Rectangle {
                                        color: parent.checked ? "#E3F2FD" : "#ffffff"
                                        border.color: parent.checked ? "#2196f3" : "#ddd"
                                        border.width: 2
                                        radius: 8
                                    }

                                    contentItem: Text {
                                        text: parent.text
                                        color: parent.checked ? "#2196f3" : "#333333"
                                        font.pixelSize: 14
                                        font.bold: parent.checked
                                        horizontalAlignment: Text.AlignHCenter
                                        verticalAlignment: Text.AlignVCenter
                                    }

                                    onClicked: {
                                        // 简化：直接更新锅炉选择
                                        selectedBoilerUI = modelData
                                        monitorWindow.dataSource.currentBoiler = modelData
                                    }
                                }
                            }

                            // 历史数据查看区域
                            RowLayout {
                                spacing: 10

                                Label {
                                    text: "历史数据:"
                                    font.pixelSize: 14
                                    color: "#666666"
                                }

                                ComboBox {
                                    id: historicalDateCombo
                                    Layout.preferredWidth: 120
                                    Layout.preferredHeight: 30
                                    model: historicalDatesModel
                                    displayText: {
                                        if (currentIndex >= 0) {
                                            return currentText
                                        } else if (historicalDatesModel.count > 0) {
                                            return "选择历史日期"
                                        } else {
                                            return "无历史数据"
                                        }
                                    }
                                    font.pixelSize: 12
                                    currentIndex: -1  // 默认不选择任何日期

                                    onCurrentTextChanged: {
                                        // 用户选择日期后直接加载历史数据
                                        if (currentText && currentText !== "" && currentIndex >= 0) {
                                            loadHistoricalData(currentText)
                                        }
                                    }
                                }

                                Button {
                                    text: "刷新"
                                    Layout.preferredWidth: 50
                                    Layout.preferredHeight: 30
                                    font.pixelSize: 10
                                    onClicked: updateHistoricalDates()

                                    background: Rectangle {
                                        color: parent.pressed ? "#e0e0e0" : "#f5f5f5"
                                        border.color: "#cccccc"
                                        border.width: 1
                                        radius: 3
                                    }
                                }

                                Button {
                                    text: "返回实时"
                                    Layout.preferredWidth: 80
                                    Layout.preferredHeight: 30
                                    font.pixelSize: 10
                                    visible: isShowingHistorical  // 只在历史模式时显示
                                    onClicked: {
                                        switchToRealTimeMode()
                                    }

                                    background: Rectangle {
                                        color: isShowingHistorical ? "#ff9800" : "#4caf50"
                                        radius: 3
                                    }

                                    contentItem: Text {
                                        text: parent.text
                                        color: "white"
                                        font.pixelSize: parent.font.pixelSize
                                        horizontalAlignment: Text.AlignHCenter
                                        verticalAlignment: Text.AlignVCenter
                                    }
                                }


                            }

                            Item {
                                Layout.fillWidth: true
                            }

                            // 显示当前选择的锅炉信息
                            RowLayout {
                                spacing: 8

                                Label {
                                    text: "当前: " + selectedBoilerUI
                                    font.pixelSize: 14
                                    font.bold: true
                                    color: "#2196f3"
                                }

                                // 简化的连接状态指示器
                                Rectangle {
                                    width: 12
                                    height: 12
                                    radius: 6
                                    color: "#4caf50"
                                    visible: selectedBoilerUI !== ""
                                }

                                Label {
                                    text: "已连接"
                                    font.pixelSize: 12
                                    color: "#4caf50"
                                    visible: selectedBoilerUI !== ""
                                }
                            }
                        }
                    }
                    */

                    // 烟气数据区域
                    Rectangle {
                        Layout.fillWidth: true
                        height: 580
                        color: "#ffffff"
                        radius: 12
                        border.color: "#e0e0e0"
                        border.width: 1

                        ColumnLayout {
                            anchors.fill: parent
                            anchors.margins: 20
                            spacing: 15

                            RowLayout {
                                Layout.fillWidth: true

                                Label {
                                    text: isShowingHistorical ?
                                          ("烟气检测设备历史数据 (" + currentHistoricalDate + ")") :
                                          "烟气检测设备实时数据"
                                    font.pixelSize: 20
                                    font.bold: true
                                    color: "#333333"
                                    Layout.fillWidth: true
                                }

                                Rectangle {
                                    width: 12
                                    height: 12
                                    radius: 6
                                    color: monitorWindow.dataSource.isDataConnected ? "#4caf50" : "#f44336"
                                }

                                Label {
                                    text: monitorWindow.dataSource.connectionStatus
                                    font.pixelSize: 14
                                    color: monitorWindow.dataSource.isDataConnected ? "#4caf50" : "#f44336"
                                    font.bold: true
                                }
                            }

                            // 当前烟气数据显示
                            Rectangle {
                                Layout.fillWidth: true
                                height: 80
                                color: monitorWindow.dataSource.isDataConnected ? "#e8f5e8" : "#ffebee"
                                radius: 8
                                border.color: monitorWindow.dataSource.isDataConnected ? "#4caf50" : "#f44336"
                                border.width: 1

                                // 根据连接状态和历史数据模式显示不同内容
                                StackLayout {
                                    anchors.fill: parent
                                    anchors.margins: 15
                                    currentIndex: {
                                        if (isShowingHistorical) {
                                            // 历史数据模式：检查是否有历史数据
                                            return historicalTableData.length > 0 ? 0 : 1
                                        } else {
                                            // 实时数据模式：检查数据连接状态
                                            return monitorWindow.dataSource.isDataConnected ? 0 : 1
                                        }
                                    }

                                    // 有数据连接时显示实际数据
                                    RowLayout {
                                        spacing: 30

                                        // 氧量
                                        RowLayout {
                                            spacing: 8
                                            Label {
                                                text: "氧量："
                                                font.pixelSize: 14
                                                color: "#333333"
                                                font.bold: true
                                            }
                                            Label {
                                                text: {
                                                    if (isShowingHistorical) {
                                                        return historicalTableData.length > 0 ?
                                                               (historicalTableData[0].o2 + "%") : "--"
                                                    } else {
                                                        return monitorWindow.dataSource.smokeTableData.length > 0 ?
                                                               (monitorWindow.dataSource.smokeTableData[0].o2 + "%") : "--"
                                                    }
                                                }
                                                font.pixelSize: 16
                                                font.bold: true
                                                color: "#388e3c"
                                            }
                                        }

                                        // CO含量
                                        RowLayout {
                                            spacing: 8
                                            Label {
                                                text: "CO含量："
                                                font.pixelSize: 14
                                                color: "#333333"
                                                font.bold: true
                                            }
                                            Label {
                                                text: {
                                                    if (isShowingHistorical) {
                                                        return historicalTableData.length > 0 ?
                                                               (historicalTableData[0].co + "ppm") : "--"
                                                    } else {
                                                        return monitorWindow.dataSource.smokeTableData.length > 0 ?
                                                               (monitorWindow.dataSource.smokeTableData[0].co + "ppm") : "--"
                                                    }
                                                }
                                                font.pixelSize: 16
                                                font.bold: true
                                                color: "#ff9800"
                                            }
                                        }







                                        // 冷凝器温度
                                        RowLayout {
                                            spacing: 8
                                            Label {
                                                text: "冷凝器温度："
                                                font.pixelSize: 14
                                                color: "#333333"
                                                font.bold: true
                                            }
                                            Label {
                                                text: {
                                                    if (isShowingHistorical) {
                                                        return historicalTableData.length > 0 ?
                                                               (historicalTableData[0].temperature + "℃") : "0.0℃"
                                                    } else {
                                                        return monitorWindow.dataSource.currentTemperature || "0.0℃"
                                                    }
                                                }
                                                font.pixelSize: 16
                                                font.bold: true
                                                color: "#e91e63"
                                            }
                                        }

                                        // 压力表
                                        RowLayout {
                                            spacing: 8
                                            Label {
                                                text: "压力表："
                                                font.pixelSize: 14
                                                color: "#333333"
                                                font.bold: true
                                            }
                                            Label {
                                                text: {
                                                    if (isShowingHistorical) {
                                                        return historicalTableData.length > 0 ?
                                                               (historicalTableData[0].voltage + "kPa") : "0.0kPa"
                                                    } else {
                                                        return monitorWindow.dataSource.currentVoltage || "0.0kPa"
                                                    }
                                                }
                                                font.pixelSize: 16
                                                font.bold: true
                                                color: "#2196f3"
                                            }
                                        }

                                        // 抽气泵电流
                                        RowLayout {
                                            spacing: 8
                                            Label {
                                                text: "抽气泵电流："
                                                font.pixelSize: 14
                                                color: "#333333"
                                                font.bold: true
                                            }
                                            Label {
                                                text: {
                                                    if (isShowingHistorical) {
                                                        return historicalTableData.length > 0 ?
                                                               (historicalTableData[0].current + "A") : "0.000A"
                                                    } else {
                                                        return monitorWindow.dataSource.currentCurrent || "0.000A"
                                                    }
                                                }
                                                font.pixelSize: 16
                                                font.bold: true
                                                color: "#ff5722"
                                            }
                                        }

                                        // 反吹反馈
                                        RowLayout {
                                            spacing: 8
                                            Label {
                                                text: "反吹反馈："
                                                font.pixelSize: 14
                                                color: "#333333"
                                                font.bold: true
                                            }
                                            Label {
                                                text: {
                                                    if (isShowingHistorical) {
                                                        return historicalTableData.length > 0 ?
                                                               (parseInt(historicalTableData[0].switch1) === 1 ? "运行" : "停止") : "停止"
                                                    } else {
                                                        // 使用与表格区域相同的数据源：smokeTableData的最新数据
                                                        return monitorWindow.dataSource.smokeTableData.length > 0 ?
                                                               (parseInt(monitorWindow.dataSource.smokeTableData[0].switch1) === 1 ? "运行" : "停止") : "停止"
                                                    }
                                                }
                                                font.pixelSize: 16
                                                font.bold: true
                                                color: {
                                                    if (isShowingHistorical) {
                                                        return historicalTableData.length > 0 && parseInt(historicalTableData[0].switch1) === 1 ? "#4caf50" : "#f44336"
                                                    } else {
                                                        return monitorWindow.dataSource.smokeSwitch1Data.length > 0 &&
                                                               monitorWindow.dataSource.smokeSwitch1Data[monitorWindow.dataSource.smokeSwitch1Data.length - 1].y === 1 ? "#4caf50" : "#f44336"
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    // 无数据连接时显示提示信息
                                    ColumnLayout {
                                        anchors.centerIn: parent
                                        spacing: 10

                                        Label {
                                            text: isShowingHistorical ? "📊 该日期暂无历史数据" : "⚠️ 未接入数据采集设备"
                                            font.pixelSize: 18
                                            font.bold: true
                                            color: "#f44336"
                                            Layout.alignment: Qt.AlignHCenter
                                        }

                                        Label {
                                            text: isShowingHistorical ?
                                                  "请选择其他日期或返回实时模式" :
                                                  "请检查串口连接和设备配置"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.alignment: Qt.AlignHCenter
                                        }
                                    }
                                }
                            }

                            // 图表容器，包含图表和滚动条
                            ColumnLayout {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                spacing: 0

                                // 图表和滚动条的容器
                                Rectangle {
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true
                                    color: "transparent"

                                    // 简化的ChartView，用于Qt 5.12兼容性
                                    ChartView {
                                        id: smokeChart
                                        anchors.fill: parent
                                        antialiasing: true
                                        backgroundColor: "#f8f9fa"

                                    title: (isShowingHistorical ?
                                           ("烟气检测设备历史数据曲线 (" + currentHistoricalDate + ")") :
                                           ("烟气检测设备实时数据曲线 (" + zoomLabels[currentZoomIndex] + ")"))
                                    titleFont.pixelSize: 16
                                    titleFont.bold: true

                                    legend.alignment: Qt.AlignBottom
                                    legend.font.pixelSize: 12

                                    // 属性定义 - 固定缩放级别
                                    property var zoomLevels: [1.0, 2.0, 3.0, 24.0]  // 对应24h, 12h, 8h, 1h
                                    property var zoomLabels: ["24小时", "12小时", "8小时", "1小时"]
                                    property int currentZoomIndex: 3  // 当前缩放级别索引，默认选中1小时
                                    property real zoomLevel: zoomLevels[currentZoomIndex]  // 当前缩放倍数

                                    // 滚动相关属性
                                    property real scrollOffset: 0  // 滚动偏移量（小时）
                                    property real maxScrollOffset: 0  // 最大滚动偏移量
                                    property bool needsScrollbar: false  // 是否需要滚动条
                                    property bool userScrolling: false  // 用户是否正在手动滚动

                                    // Tooltip相关属性
                                    property bool showTooltip: false
                                    property real tooltipX: 0
                                    property real tooltipY: 0
                                    property string tooltipText: ""

                                    ValueAxis {
                                        id: smokeAxisX
                                        // 优化：统一使用分钟作为时间单位
                                        titleText: "时间 (分钟)"

                                        // 优化：根据缩放级别设置分钟范围
                                        min: 0
                                        max: {
                                            switch(smokeChart.currentZoomIndex) {
                                                case 0: return 24 * 60;  // 24小时 = 1440分钟
                                                case 1: return 12 * 60;  // 12小时 = 720分钟
                                                case 2: return 8 * 60;   // 8小时 = 480分钟
                                                case 3: return 60;       // 1小时 = 60分钟
                                                default: return 24 * 60;
                                            }
                                        }

                                        tickCount: {
                                            switch(smokeChart.currentZoomIndex) {
                                                case 0: return 13   // 24小时：每2小时一个刻度 (0,120,240,360,480,600,720,840,960,1080,1200,1320,1440)
                                                case 1: return 7    // 12小时：每2小时一个刻度 (0,120,240,360,480,600,720)
                                                case 2: return 9    // 8小时：每1小时一个刻度 (0,60,120,180,240,300,360,420,480)
                                                case 3: return 7    // 1小时：每10分钟一个刻度 (0,10,20,30,40,50,60)
                                                default: return 13
                                            }
                                        }

                                        labelFormat: "%.0f"  // 显示整数分钟
                                    }

                                    // 相对时间轴不需要复杂的时间范围计算
                                    // X轴范围已经在ValueAxis中直接设置

                                    // 简化的缩放变化处理
                                    onCurrentZoomIndexChanged: {
                                        zoomLevel = zoomLevels[currentZoomIndex]

                                        // 重置滚动状态
                                        scrollOffset = 0
                                        updateScrollbarState()

                                        // 清空现有的图表数据，防止线条交叉
                                        smokeO2Series.clear()
                                        smokeCOSeries.clear()

                                        // 简化的图表重绘逻辑
                                        Qt.callLater(function() {
                                            if (isShowingHistorical) {
                                                // 历史数据模式
                                                if (currentHistoricalData.length > 0) {
                                                    updateChartWithHistoricalData(currentHistoricalData)
                                                }
                                            } else {
                                                // 实时数据模式
                                                updateChartData()
                                            }
                                        })
                                    }

                                // 左侧Y轴：O₂ 氧量 (0-25%)
                                ValueAxis {
                                    id: smokeAxisY_Left
                                    min: 0
                                    max: 25
                                    titleText: "O₂ (%)"
                                    labelFormat: "%.1f"
                                    color: "#388e3c"
                                }

                                // 右侧Y轴：CO (0-5000)
                                ValueAxis {
                                    id: smokeAxisY_Right
                                    min: 0
                                    max: 5000
                                    titleText: "CO(ppm)"
                                    labelFormat: "%.0f"
                                    color: "#666666"
                                }



                                LineSeries {
                                    id: smokeO2Series
                                    name: "O₂ (%)"
                                    color: "#388e3c"
                                    width: 2
                                    axisX: smokeAxisX
                                    axisY: smokeAxisY_Left
                                }

                                LineSeries {
                                    id: smokeCOSeries
                                    name: "CO (ppm)"
                                    color: "#ff9800"
                                    width: 2
                                    axisX: smokeAxisX
                                    axisYRight: smokeAxisY_Right
                                }

                                // 简化的数据更新信号连接
                                Connections {
                                    target: monitorWindow.dataSource
                                    function onChartDataUpdated() {
                                        // 简单直接：收到信号就更新图表
                                        if (!isShowingHistorical && monitorWindow.dataSource.isRunning) {
                                            console.log("收到图表更新信号，开始更新图表")
                                            updateChartData()
                                        }
                                    }

                                    function onSmokeDataChanged() {
                                        // 简单直接：数据变化就更新滚动条
                                        if (!isShowingHistorical) {
                                            updateScrollbarState()
                                        }
                                    }

                                    function onCurrentBoilerChanged() {
                                        // 锅炉切换时立即更新图表（确保有初始数据显示）
                                        if (!isShowingHistorical && monitorWindow.dataSource.isRunning) {
                                            console.log("锅炉切换，立即更新图表")
                                            Qt.callLater(function() {
                                                updateChartData()
                                            })
                                        }
                                    }
                                }

                                    // 鼠标交互区域 - 包含滚轮缩放和悬浮显示数据
                                    MouseArea {
                                        anchors.fill: parent
                                        acceptedButtons: Qt.NoButton
                                        hoverEnabled: true

                                        onWheel: {
                                            var delta = wheel.angleDelta.y

                                            // 纯粹的横向滚动功能，类似浏览器滚动条
                                            // 检查是否需要滚动条（即内容是否超出了当前时间范围）
                                            if (smokeChart.needsScrollbar && smokeChart.maxScrollOffset > 0) {
                                                // 内容超出时间范围，进行横向滚动
                                                var scrollStep = 0.5  // 滚动步长（小时）

                                                // 根据当前缩放级别调整滚动步长
                                                switch(smokeChart.currentZoomIndex) {
                                                    case 0: scrollStep = 1.0; break    // 24小时视图：1小时步长
                                                    case 1: scrollStep = 0.5; break    // 12小时视图：0.5小时步长
                                                    case 2: scrollStep = 0.25; break   // 8小时视图：0.25小时步长
                                                    case 3: scrollStep = 2.0; break    // 1小时视图：2分钟步长（以分钟为单位）
                                                }

                                                if (delta > 0) {
                                                    // 向前滚动（显示更早的数据）
                                                    smokeChart.scrollOffset = Math.max(0, smokeChart.scrollOffset - scrollStep)
                                                } else {
                                                    // 向后滚动（显示更新的数据）
                                                    smokeChart.scrollOffset = Math.min(smokeChart.maxScrollOffset, smokeChart.scrollOffset + scrollStep)
                                                }

                                                // 标记用户正在滚动
                                                smokeChart.userScrolling = true

                                                // 更新图表显示
                                                updateChartWithScroll()

                                                // 延迟重置滚动状态
                                                userScrollResetTimer.restart()

                                                wheel.accepted = true
                                            } else {
                                                // 内容未超出时间范围，允许事件传播（但不做任何缩放处理）
                                                wheel.accepted = false
                                            }
                                        }

                                        onPositionChanged: {
                                            updateTooltip(mouse.x, mouse.y)
                                        }

                                        onEntered: {
                                            smokeChart.showTooltip = true
                                        }

                                        onExited: {
                                            smokeChart.showTooltip = false
                                        }

                                        // 更新tooltip的函数
                                        function updateTooltip(mouseX, mouseY) {
                                            if (!smokeChart.showTooltip) return

                                            // 将鼠标坐标转换为图表坐标
                                            var chartPoint = smokeChart.mapToValue(Qt.point(mouseX, mouseY), smokeO2Series)
                                            var timeValue = chartPoint.x

                                            // 查找最接近的数据点
                                            var closestData = findClosestDataPoint(timeValue)
                                            if (closestData) {
                                                smokeChart.tooltipX = mouseX
                                                smokeChart.tooltipY = mouseY
                                                smokeChart.tooltipText = formatTooltipText(closestData, timeValue)
                                            } else {
                                                // 没有找到合适的数据点，隐藏tooltip
                                                smokeChart.tooltipText = ""
                                            }
                                        }

                                        // 查找最接近的数据点
                                        function findClosestDataPoint(timeValue) {
                                            // 在历史数据模式下，使用历史数据
                                            var dataSource = null
                                            if (isShowingHistorical && currentHistoricalData.length > 0) {
                                                dataSource = currentHistoricalData
                                            } else if (monitorWindow.dataSource.smokeO2Data && monitorWindow.dataSource.smokeO2Data.length > 0) {
                                                // 实时数据模式，使用实时数据
                                            } else {
                                                return null
                                            }

                                            var minDistance = Number.MAX_VALUE
                                            var closestIndex = -1
                                            var useMinutes = smokeChart.currentZoomIndex === 3 // 1小时视图使用分钟

                                            // 设置距离阈值：只有当鼠标足够接近数据点时才显示tooltip
                                            var maxAllowedDistance = useMinutes ? 0.5 : 0.1  // 分钟模式：0.5分钟，小时模式：0.1小时

                                            if (isShowingHistorical && currentHistoricalData.length > 0) {
                                                // 历史数据模式：直接从历史数据中查找
                                                for (var i = 0; i < currentHistoricalData.length; i++) {
                                                    var dataPoint = currentHistoricalData[i]

                                                    // 计算显示时间（考虑时间偏移）
                                                    var firstTimestamp = currentHistoricalData[0].timestamp
                                                    var relativeTimeSeconds = dataPoint.timestamp - firstTimestamp
                                                    var relativeTime = useMinutes ?
                                                        (relativeTimeSeconds / 60.0) : (relativeTimeSeconds / 3600.0)

                                                    // 应用滚动偏移（如果有的话）
                                                    var displayTime = relativeTime - (smokeChart.scrollOffset || 0)
                                                    var distance = Math.abs(displayTime - timeValue)

                                                    if (distance < minDistance) {
                                                        minDistance = distance
                                                        closestIndex = i
                                                    }
                                                }

                                                if (closestIndex >= 0 && minDistance <= maxAllowedDistance) {
                                                    var data = currentHistoricalData[closestIndex]
                                                    return {
                                                        time: timeValue,
                                                        o2: data.o2 || 0,
                                                        co: data.co || 0,
                                                        index: closestIndex
                                                    }
                                                }
                                            } else {
                                                // 实时数据模式：使用原有逻辑
                                                for (var j = 0; j < monitorWindow.dataSource.smokeO2Data.length; j++) {
                                                    var dataPoint = monitorWindow.dataSource.smokeO2Data[j]
                                                    var dataTime = useMinutes ? dataPoint.x_minutes : dataPoint.x

                                                    // 考虑滚动偏移
                                                    var displayTime = dataTime - (useMinutes ? 0 : smokeChart.scrollOffset)
                                                    var distance = Math.abs(displayTime - timeValue)

                                                    if (distance < minDistance) {
                                                        minDistance = distance
                                                        closestIndex = j
                                                    }
                                                }

                                                if (closestIndex >= 0 && minDistance <= maxAllowedDistance) {
                                                    var o2Data = monitorWindow.dataSource.smokeO2Data[closestIndex]
                                                    var coData = monitorWindow.dataSource.smokeCOData[closestIndex]
                                                    return {
                                                        time: useMinutes ? o2Data.x_minutes : o2Data.x,
                                                        o2: o2Data.y,
                                                        co: coData.y,
                                                        index: closestIndex
                                                    }
                                                }
                                            }

                                            return null
                                        }

                                        // 格式化tooltip文本
                                        function formatTooltipText(data, timeValue) {
                                            return "O₂: " + data.o2.toFixed(2) + "%\n" +
                                                   "CO: " + data.co.toFixed(0) + "ppm"
                                        }
                                    }

                                    // 相对时间轴不需要定时更新

                                    Component.onCompleted: {
                                        // 初始化时更新一次图表数据
                                        if (smokeChart.currentZoomIndex === 3) {
                                            monitorWindow.dataSource.updateSmokeChartSeriesWithMinutes(
                                                smokeO2Series, smokeCOSeries
                                            )
                                        } else {
                                            monitorWindow.dataSource.updateSmokeChartSeries(
                                                smokeO2Series, smokeCOSeries,
                                                smokeChart.currentZoomIndex
                                            )
                                        }
                                    }

                                    // Tooltip显示组件
                                    Rectangle {
                                        id: tooltip
                                        visible: smokeChart.showTooltip && smokeChart.tooltipText !== ""
                                        x: Math.min(smokeChart.tooltipX + 10, smokeChart.width - width - 10)
                                        y: Math.max(10, smokeChart.tooltipY - height - 10)
                                        width: tooltipText.implicitWidth + 20
                                        height: tooltipText.implicitHeight + 16
                                        color: "#2c3e50"
                                        border.color: "#34495e"
                                        border.width: 1
                                        radius: 6
                                        z: 1000

                                        // 添加阴影效果
                                        Rectangle {
                                            anchors.fill: parent
                                            anchors.topMargin: 2
                                            anchors.leftMargin: 2
                                            color: "#000000"
                                            opacity: 0.2
                                            radius: parent.radius
                                            z: -1
                                        }

                                        Text {
                                            id: tooltipText
                                            anchors.centerIn: parent
                                            text: smokeChart.tooltipText
                                            color: "#ecf0f1"
                                            font.pixelSize: 11
                                            font.family: "Consolas, Monaco, monospace"
                                            lineHeight: 1.2
                                        }

                                        // 小三角形指示器
                                        Canvas {
                                            id: tooltipArrow
                                            width: 8
                                            height: 8
                                            x: Math.max(8, Math.min(parent.width - 16, smokeChart.tooltipX - tooltip.x))
                                            y: parent.height
                                            visible: tooltip.visible

                                            onPaint: {
                                                var ctx = getContext("2d")
                                                ctx.clearRect(0, 0, width, height)
                                                ctx.fillStyle = "#2c3e50"
                                                ctx.beginPath()
                                                ctx.moveTo(4, 0)
                                                ctx.lineTo(8, 8)
                                                ctx.lineTo(0, 8)
                                                ctx.closePath()
                                                ctx.fill()
                                            }
                                        }
                                    }
                                }

                                // 水平滚动条容器
                                Rectangle {
                                    id: scrollbarContainer
                                    anchors.left: parent.left
                                    anchors.right: parent.right
                                    anchors.bottom: parent.bottom
                                    height: smokeChart.needsScrollbar ? 20 : 0
                                    color: "#f0f0f0"
                                    border.color: "#cccccc"
                                    border.width: smokeChart.needsScrollbar ? 1 : 0
                                    visible: smokeChart.needsScrollbar

                                    // 滚动条背景
                                    Rectangle {
                                        id: scrollbarBackground
                                        anchors.fill: parent
                                        anchors.margins: 2
                                        color: "#e0e0e0"
                                        radius: 2

                                        // 全局拖动捕获区域 - 覆盖整个滚动条背景
                                        MouseArea {
                                            id: globalScrollArea
                                            anchors.fill: parent
                                            hoverEnabled: true

                                            property bool isDragging: false
                                            property real dragStartX: 0
                                            property real dragStartOffset: 0
                                            property real handleStartX: 0

                                            // 动态光标形状
                                            cursorShape: {
                                                if (isDragging) return Qt.ClosedHandCursor

                                                var handleRect = getHandleRect()
                                                var mx = mouseX

                                                if (mx >= handleRect.x && mx <= (handleRect.x + handleRect.width)) {
                                                    return Qt.OpenHandCursor  // 在滑块上显示可拖动光标
                                                } else {
                                                    return Qt.PointingHandCursor  // 在背景上显示可点击光标
                                                }
                                            }

                                            onPressed: {
                                                if (smokeChart.maxScrollOffset <= 0) return

                                                var handleRect = getHandleRect()
                                                var clickX = mouseX

                                                // 检查是否点击在滑块上
                                                if (clickX >= handleRect.x && clickX <= (handleRect.x + handleRect.width)) {
                                                    // 点击在滑块上，开始拖动
                                                    isDragging = true
                                                    dragStartX = clickX
                                                    dragStartOffset = smokeChart.scrollOffset
                                                    handleStartX = handleRect.x
                                                    smokeChart.userScrolling = true


                                                } else {
                                                    // 点击在背景上，快速跳转
                                                    var clickRatio = clickX / width
                                                    clickRatio = Math.max(0.0, Math.min(1.0, clickRatio))

                                                    smokeChart.scrollOffset = clickRatio * smokeChart.maxScrollOffset
                                                    smokeChart.userScrolling = true



                                                    updateChartWithScroll()
                                                }
                                            }

                                            onReleased: {
                                                if (isDragging) {

                                                    isDragging = false
                                                    // 确保最后一次更新图表
                                                    updateChartWithScroll()
                                                }
                                            }

                                            onPositionChanged: {
                                                if (isDragging && smokeChart.maxScrollOffset > 0) {
                                                    var deltaX = mouseX - dragStartX
                                                    var handleRect = getHandleRect()
                                                    var maxDragRange = width - handleRect.width

                                                    if (maxDragRange > 0) {
                                                        var deltaOffset = (deltaX / maxDragRange) * smokeChart.maxScrollOffset
                                                        var newOffset = Math.max(0, Math.min(smokeChart.maxScrollOffset,
                                                                                            dragStartOffset + deltaOffset))

                                                        // 直接更新偏移量，不触发绑定更新
                                                        smokeChart.scrollOffset = newOffset

                                                        // 直接更新图表
                                                        updateChartWithScroll()
                                                    }
                                                }
                                            }

                                            // 计算滑块矩形的辅助函数
                                            function getHandleRect() {
                                                var handleWidth = calculateHandleWidth()
                                                var maxX = width - handleWidth
                                                var ratio = smokeChart.maxScrollOffset > 0 ?
                                                          (smokeChart.scrollOffset / smokeChart.maxScrollOffset) : 0
                                                var x = Math.round(ratio * maxX)

                                                return {
                                                    x: Math.max(0, Math.min(maxX, x)),
                                                    width: handleWidth
                                                }
                                            }

                                            function calculateHandleWidth() {
                                                if (smokeChart.maxScrollOffset <= 0) return width

                                                var displayRange = 24.0
                                                switch(smokeChart.currentZoomIndex) {
                                                    case 0: displayRange = 24.0; break
                                                    case 1: displayRange = 12.0; break
                                                    case 2: displayRange = 8.0; break
                                                    case 3: displayRange = 60.0; break
                                                }

                                                var totalDataRange = displayRange + smokeChart.maxScrollOffset
                                                var ratio = Math.max(0.1, Math.min(1.0, displayRange / totalDataRange))
                                                var calculatedWidth = width * ratio

                                                return Math.max(30, Math.min(width, calculatedWidth))
                                            }
                                        }

                                        // 滚动条滑块 - 使用独立位置控制
                                        Rectangle {
                                            id: scrollHandle
                                            height: parent.height

                                            // 滑块宽度计算
                                            width: globalScrollArea.calculateHandleWidth()

                                            // 滑块位置 - 使用独立计算，避免绑定冲突
                                            x: {
                                                if (globalScrollArea.isDragging) {
                                                    // 拖动时使用实时计算的位置
                                                    return globalScrollArea.getHandleRect().x
                                                } else {
                                                    // 非拖动时使用绑定位置
                                                    if (smokeChart.maxScrollOffset <= 0) return 0

                                                    var maxX = parent.width - width
                                                    if (maxX <= 0) return 0

                                                    var ratio = smokeChart.scrollOffset / smokeChart.maxScrollOffset
                                                    ratio = Math.max(0.0, Math.min(1.0, ratio))

                                                    return Math.round(ratio * maxX)
                                                }
                                            }

                                            color: {
                                                if (globalScrollArea.isDragging) return "#1976d2"
                                                if (globalScrollArea.containsMouse) return "#2196f3"
                                                return "#4caf50"
                                            }
                                            radius: 2

                                            // 只在非拖动状态下启用平滑动画
                                            Behavior on x {
                                                enabled: !globalScrollArea.isDragging
                                                NumberAnimation {
                                                    duration: 150
                                                    easing.type: Easing.OutQuad
                                                }
                                            }

                                            // 滑块宽度变化时的平滑动画
                                            Behavior on width {
                                                NumberAnimation {
                                                    duration: 200
                                                    easing.type: Easing.OutQuad
                                                }
                                            }

                                            // 滑块上的指示文本和视觉效果
                                            Text {
                                                anchors.centerIn: parent
                                                text: "◄ ►"
                                                color: "white"
                                                font.pixelSize: 8
                                                opacity: {
                                                    if (globalScrollArea.isDragging) return 1.0
                                                    if (globalScrollArea.containsMouse) {
                                                        var handleRect = globalScrollArea.getHandleRect()
                                                        var mouseX = globalScrollArea.mouseX
                                                        return (mouseX >= handleRect.x && mouseX <= (handleRect.x + handleRect.width)) ? 1.0 : 0.7
                                                    }
                                                    return 0.7
                                                }

                                                Behavior on opacity {
                                                    NumberAnimation { duration: 150 }
                                                }
                                            }

                                            // 拖动时的视觉反馈
                                            Rectangle {
                                                anchors.fill: parent
                                                color: "white"
                                                opacity: globalScrollArea.isDragging ? 0.2 : 0.0
                                                radius: parent.radius

                                                Behavior on opacity {
                                                    NumberAnimation { duration: 100 }
                                                }
                                            }
                                        }
                                    }


                                }
                            }

                                // 缩放控制按钮 - 固定级别按钮
                                RowLayout {
                                    Layout.fillWidth: true
                                    Layout.preferredHeight: 30
                                    spacing: 10

                                    Label {
                                        text: "时间范围:"
                                        font.pixelSize: 12
                                        color: "#666666"
                                    }

                                    // 动态生成缩放级别按钮
                                    Repeater {
                                        model: smokeChart.zoomLabels

                                        Button {
                                            text: modelData
                                            Layout.preferredWidth: 60
                                            Layout.preferredHeight: 25
                                            font.pixelSize: 10

                                            // 当前选中的按钮高亮显示
                                            property bool isSelected: smokeChart.currentZoomIndex === index

                                            onClicked: {
                                                // 防止重复点击
                                                if (index === smokeChart.currentZoomIndex) {
                                                    return
                                                }

                                                // 简化：直接切换时间范围
                                                smokeChart.currentZoomIndex = index
                                                performZoomSwitch(index)
                                            }

                                            background: Rectangle {
                                                color: parent.isSelected ? "#2196f3" : (parent.pressed ? "#e0e0e0" : "#f5f5f5")
                                                border.color: parent.isSelected ? "#1976d2" : "#cccccc"
                                                border.width: 1
                                                radius: 3
                                            }

                                            contentItem: Text {
                                                text: parent.text
                                                color: parent.isSelected ? "white" : "#333333"
                                                font.pixelSize: parent.font.pixelSize
                                                font.bold: parent.isSelected
                                                horizontalAlignment: Text.AlignHCenter
                                                verticalAlignment: Text.AlignVCenter
                                            }
                                        }
                                    }

                                    Item {
                                        Layout.fillWidth: true
                                    }

                                    Label {
                                        text: "当前视图: " + smokeChart.zoomLabels[smokeChart.currentZoomIndex]
                                        font.pixelSize: 11
                                        color: "#666666"
                                        font.bold: true
                                    }
                                }
                            }
                        }
                    }

                    // 烟气数据表格
                    Rectangle {
                        Layout.fillWidth: true
                        height: 300
                        color: "#ffffff"
                        radius: 12
                        border.color: "#e0e0e0"
                        border.width: 1

                        ColumnLayout {
                            anchors.fill: parent
                            anchors.margins: 20
                            spacing: 15

                            RowLayout {
                                Layout.fillWidth: true

                                Label {
                                    text: "烟气数据记录"
                                    font.pixelSize: 18
                                    font.bold: true
                                    color: "#333333"
                                    Layout.fillWidth: true
                                }

                                Label {
                                    text: {
                                        if (isShowingHistorical) {
                                            return "历史数据 (" + historicalTableData.length + " 条记录)"
                                        } else {
                                            var count = monitorWindow.dataSource.smokeTableData.length
                                            return "实时数据 (" + count + " 条记录)"
                                        }
                                    }
                                    font.pixelSize: 12
                                    color: "#666666"
                                }
                            }

                            // 固定表头
                            Rectangle {
                                Layout.fillWidth: true
                                height: 40
                                color: "#e8f5e8"
                                border.color: "#4caf50"
                                border.width: 1
                                z: 1

                                Row {
                                    anchors.fill: parent
                                    anchors.margins: 10
                                    spacing: 10

                                    Label { text: "时间"; width: 160; font.bold: true; color: "#2e7d32"; font.pixelSize: 11 }
                                    Label { text: "氧量(%)"; width: 70; font.bold: true; color: "#2e7d32"; font.pixelSize: 11 }
                                    Label { text: "CO(ppm)"; width: 80; font.bold: true; color: "#2e7d32"; font.pixelSize: 11 }
                                    Label { text: "冷凝器温度(℃)"; width: 90; font.bold: true; color: "#2e7d32"; font.pixelSize: 11 }
                                    Label { text: "压力表(kPa)"; width: 80; font.bold: true; color: "#2e7d32"; font.pixelSize: 11 }
                                    Label { text: "抽气泵电流(A)"; width: 90; font.bold: true; color: "#2e7d32"; font.pixelSize: 11 }
                                    Label { text: "反吹反馈"; width: 60; font.bold: true; color: "#2e7d32"; font.pixelSize: 11 }
                                }
                            }

                            Rectangle {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: "#ffffff"
                                border.color: "#e0e0e0"
                                border.width: 1

                                StackLayout {
                                    anchors.fill: parent
                                    currentIndex: {
                                        if (isShowingHistorical) {
                                            // 历史数据模式：检查是否有历史表格数据
                                            return historicalTableData.length > 0 ? 0 : 1
                                        } else {
                                            // 实时数据模式：检查是否有实时数据连接和表格数据
                                            return (monitorWindow.dataSource.isDataConnected && monitorWindow.dataSource.smokeTableData.length > 0) ? 0 : 1
                                        }
                                    }

                                    // 有数据时显示表格
                                    ListView {
                                        model: isShowingHistorical ? historicalTableData : monitorWindow.dataSource.smokeTableData
                                        clip: true
                                        boundsBehavior: Flickable.StopAtBounds

                                        // 防止滚动事件向上传播到外层ScrollView
                                        flickableDirection: Flickable.VerticalFlick
                                        interactive: true

                                        // 滚动条
                                        ScrollBar.vertical: ScrollBar {
                                            active: true
                                            policy: ScrollBar.AsNeeded
                                        }

                                        // 添加鼠标区域来拦截滚动事件
                                        MouseArea {
                                            anchors.fill: parent
                                            acceptedButtons: Qt.NoButton
                                            onWheel: {
                                                // 检查ListView是否可以处理滚动
                                                var listView = parent
                                                var canScrollUp = listView.contentY > 0
                                                var canScrollDown = listView.contentY < (listView.contentHeight - listView.height)

                                                // 如果ListView可以滚动，则阻止事件传播
                                                if ((wheel.angleDelta.y > 0 && canScrollUp) ||
                                                    (wheel.angleDelta.y < 0 && canScrollDown)) {
                                                    wheel.accepted = true

                                                    // 手动处理ListView滚动
                                                    var delta = wheel.angleDelta.y
                                                    var scrollAmount = delta > 0 ? -30 : 30  // 滚动步长
                                                    listView.contentY = Math.max(0,
                                                        Math.min(listView.contentHeight - listView.height,
                                                                listView.contentY + scrollAmount))
                                                } else {
                                                    // ListView已到边界，允许事件传播给外层ScrollView
                                                    wheel.accepted = false
                                                }
                                            }
                                        }

                                        delegate: Rectangle {
                                            width: parent ? parent.width : 800  // 防止parent为null的错误
                                            height: 35
                                            color: index % 2 === 0 ? "#ffffff" : "#f5f5f5"
                                            border.color: "#e0e0e0"
                                            border.width: index === 0 ? 0 : 1

                                            Row {
                                                anchors.fill: parent
                                                anchors.margins: 10
                                                spacing: 10

                                                Label {
                                                    text: modelData.time
                                                    width: 160
                                                    font.pixelSize: 10
                                                    color: "#333333"
                                                }
                                                Label {
                                                    text: modelData.o2 + "%"
                                                    width: 70
                                                    font.pixelSize: 10
                                                    color: "#388e3c"
                                                    font.bold: true
                                                }
                                                Label {
                                                    text: modelData.co + "ppm"
                                                    width: 80
                                                    font.pixelSize: 10
                                                    color: "#ff9800"
                                                    font.bold: true
                                                }

                                                Label {
                                                    text: (modelData.temperature || "0.0") + "℃"
                                                    width: 70
                                                    font.pixelSize: 10
                                                    color: "#e91e63"
                                                    font.bold: true
                                                }
                                                Label {
                                                    text: (modelData.voltage || "0.0") + "V"
                                                    width: 70
                                                    font.pixelSize: 10
                                                    color: "#2196f3"
                                                    font.bold: true
                                                }
                                                Label {
                                                    text: (modelData.current || "0.000") + "A"
                                                    width: 70
                                                    font.pixelSize: 10
                                                    color: "#ff5722"
                                                    font.bold: true
                                                }
                                                Label {
                                                    text: (parseInt(modelData.switch1) === 1) ? "运行" : "停止"
                                                    width: 60
                                                    font.pixelSize: 10
                                                    color: (parseInt(modelData.switch1) === 1) ? "#4caf50" : "#f44336"
                                                    font.bold: true
                                                }
                                            }
                                        }
                                    }

                                    // 无数据时显示提示
                                    ColumnLayout {
                                        anchors.centerIn: parent
                                        spacing: 15

                                        Label {
                                            text: isShowingHistorical ? "📊 该日期暂无数据记录" : "📊 暂无数据记录"
                                            font.pixelSize: 16
                                            font.bold: true
                                            color: "#999999"
                                            Layout.alignment: Qt.AlignHCenter
                                        }

                                        Label {
                                            text: {
                                                if (isShowingHistorical) {
                                                    return "请选择其他日期或返回实时模式"
                                                } else {
                                                    return monitorWindow.dataSource.isDataConnected ?
                                                          "等待数据采集..." : "请先连接数据采集设备"
                                                }
                                            }
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.alignment: Qt.AlignHCenter
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // 移除重复的Connections，已在图表区域处理

    // 页面可见性变化时的处理
    onVisibleChanged: {
        if (visible && !isShowingHistorical) {
            console.log("页面变为可见，更新图表数据")
            Qt.callLater(function() {
                updateChartData()
            })
        }
    }

    // 页面加载完成后自动开始监控
    Component.onCompleted: {
        // 确保默认是实时模式
        isShowingHistorical = false

        // 启动实时监控
        monitorWindow.startMonitoring()

        // 简化的初始化
        Qt.callLater(function() {
            // 初始化历史数据列表
            updateHistoricalDates()

            // 触发图表初始化 - 多次尝试确保数据加载
            console.log("页面初始化，更新图表数据")
            updateChartData()

            // 延迟再次尝试，确保数据源已准备好
            Qt.callLater(function() {
                console.log("延迟再次更新图表数据")
                updateChartData()
            })
        })

        // MonitoringDataSource现在使用智能检测机制，QML端不再需要额外的延迟
    }



    // 简化的清理函数
    function performQuickCleanup() {
        // 停止监控
        if (monitorWindow.dataSource.isRunning) {
            monitorWindow.stopMonitoring()
        }

        // 清理图表数据
        smokeO2Series.clear()
        smokeCOSeries.clear()

        // 重置状态
        resetPageState()
    }

    // 重置页面状态
    function resetPageState() {
        isShowingHistorical = false
        currentHistoricalDate = ""
        currentHistoricalData = []
        historicalTableData = []
        isReturningHome = false
    }

    // 简化的时间范围切换函数
    function performZoomSwitch(zoomIndex) {
        // 清空现有的图表数据
        smokeO2Series.clear()
        smokeCOSeries.clear()

        // 重新绘制图表
        if (!isShowingHistorical) {
            updateChartData()
        }
    }

    // 完整清理资源的函数 - 保留作为备用
    function performCleanup() {
        performQuickCleanup()
    }

    // 页面销毁时清理资源（保留作为备用）
    Component.onDestruction: {
        // 如果页面被强制销毁，只执行最关键的清理
        if (monitorWindow.dataSource.isRunning) {
            monitorWindow.stopMonitoring()
        }
        chartUpdateTimer.stop()
    }



    // 历史数据相关的数据模型
    ListModel {
        id: historicalDatesModel
    }

    ListModel {
        id: historicalDataModel
    }

    // 历史数据相关函数
    function updateHistoricalDates() {
        historicalDatesModel.clear()

        if (typeof csvReader !== 'undefined' && selectedBoilerUI !== "") {
            var dates = csvReader.getAvailableDates(selectedBoilerUI)

            for (var i = 0; i < dates.length; i++) {
                historicalDatesModel.append({"text": dates[i]})
            }

            // 不自动选择日期，保持默认的实时模式
            historicalDateCombo.currentIndex = -1
        }
    }

    function loadHistoricalData(dateString) {
        if (typeof csvReader === 'undefined' || !dateString || selectedBoilerUI === "") {
            return
        }

        try {
            var date = new Date(dateString)
            var data = csvReader.readDataByDate(selectedBoilerUI, date)

            if (data.length > 0) {
                // 切换到历史数据模式
                isShowingHistorical = true
                currentHistoricalDate = dateString
                currentHistoricalData = data  // 保存历史数据

                // 转换历史数据为表格格式
                convertHistoricalDataToTableFormat(data)

                // 更新图表显示历史数据
                updateChartWithHistoricalData(data)

                // 停止实时数据更新
                if (monitorWindow.dataSource.isRunning) {
                    monitorWindow.stopMonitoring()
                }
            } else {
                // 没有找到历史数据
            }
        } catch (error) {
            // 加载历史数据时出错
        }
    }

    function updateChartWithHistoricalData(data) {
        // 清空现有图表数据
        smokeO2Series.clear()
        smokeCOSeries.clear()

        if (data.length === 0) {
            return
        }

        // 获取第一个和最后一个数据点的时间戳
        var firstTimestamp = data[0].timestamp
        var lastTimestamp = data[data.length - 1].timestamp
        var totalTimeSeconds = lastTimestamp - firstTimestamp
        var totalTimeHours = totalTimeSeconds / 3600.0  // 转换为小时
        var totalTimeMinutes = totalTimeSeconds / 60.0  // 转换为分钟

        // 根据当前缩放级别确定时间单位和显示范围
        var useMinutes = smokeChart.currentZoomIndex === 3  // 1小时视图使用分钟
        var displayRange = 24.0  // 默认24小时
        switch(smokeChart.currentZoomIndex) {
            case 0: displayRange = 24.0; break   // 24小时
            case 1: displayRange = 12.0; break   // 12小时
            case 2: displayRange = 8.0; break    // 8小时
            case 3: displayRange = 60.0; break   // 1小时 = 60分钟
        }

        // 计算历史数据的时间范围（用于滚动条计算）
        var dataTimeRange = useMinutes ? totalTimeMinutes : totalTimeHours

        // 计算滚动偏移量：如果数据超过显示范围，让最新数据显示在范围末尾
        var timeOffset = 0.0
        if (dataTimeRange > displayRange) {
            // 历史数据模式：默认显示最新的数据（右侧对齐）
            timeOffset = dataTimeRange - displayRange

            // 设置滚动条状态
            smokeChart.needsScrollbar = true
            smokeChart.maxScrollOffset = timeOffset
            smokeChart.scrollOffset = smokeChart.userScrolling ?
                Math.min(smokeChart.scrollOffset, timeOffset) : timeOffset
        } else {
            // 数据在显示范围内，不需要滚动条
            smokeChart.needsScrollbar = false
            smokeChart.maxScrollOffset = 0
            smokeChart.scrollOffset = 0
        }

        // 添加历史数据到图表
        for (var i = 0; i < data.length; i++) {
            var point = data[i]
            var pointTimestamp = point.timestamp

            // 计算相对时间（从第一个数据点开始的时间）
            var relativeTimeSeconds = pointTimestamp - firstTimestamp
            var relativeTime = useMinutes ?
                (relativeTimeSeconds / 60.0) :     // 分钟
                (relativeTimeSeconds / 3600.0)     // 小时

            // 应用滚动偏移量
            var displayTime = relativeTime - timeOffset

            // 只显示在显示范围内的数据点
            if (displayTime >= 0.0 && displayTime <= displayRange) {
                smokeO2Series.append(displayTime, point.o2 || 0)
                smokeCOSeries.append(displayTime, point.co || 0)
            }
        }
    }

    function switchToRealTimeMode() {

        // 重置历史数据状态
        isShowingHistorical = false
        currentHistoricalDate = ""
        currentHistoricalData = []  // 清空历史数据
        historicalTableData = []    // 清空历史表格数据

        // 重置日期选择
        historicalDateCombo.currentIndex = -1

        // 清空图表数据，让实时数据重新填充
        smokeO2Series.clear()
        smokeCOSeries.clear()

        // 确保实时监控正在运行
        if (!monitorWindow.dataSource.isRunning) {
            monitorWindow.startMonitoring()
        }

        // 重置滚动条状态，恢复自动跟随
        smokeChart.userScrolling = false
        updateScrollbarState()
    }





    // 监听滚动偏移量变化，确保滑块位置同步
    Connections {
        target: smokeChart
        function onScrollOffsetChanged() {
            // 只在非拖动状态下强制更新滑块位置
            if (!globalScrollArea.isDragging) {
                // 触发滑块位置重新计算
                scrollHandle.x = scrollHandle.x
            }
        }
    }

    // 监听锅炉切换，更新历史数据列表
    onSelectedBoilerUIChanged: {
        if (selectedBoilerUI !== "") {
            updateHistoricalDates()
        }
    }

    // 滚动相关函数
    function updateScrollbarState() {
        // 历史数据模式：滚动条状态由updateChartWithHistoricalData函数管理
        if (isShowingHistorical) {
            return
        }

        // 实时数据模式：只有在12h、8h、1h视图时才可能需要滚动条
        if (smokeChart.currentZoomIndex === 0) {
            // 24小时视图不需要滚动条
            smokeChart.needsScrollbar = false
            smokeChart.maxScrollOffset = 0
            smokeChart.scrollOffset = 0
            return
        }

        // 计算当前数据的实际时间范围
        var currentDataTimeHours = 0
        var dataLength = 0

        // 检查是否有数据
        if (typeof monitorWindow !== 'undefined' &&
            typeof monitorWindow.dataSource !== 'undefined' &&
            typeof monitorWindow.dataSource.smokeO2Data !== 'undefined') {
            dataLength = monitorWindow.dataSource.smokeO2Data.length

            if (dataLength > 0) {
                // 获取最新数据点的时间（相对于开始采集的时间）
                var lastPoint = monitorWindow.dataSource.smokeO2Data[dataLength - 1]
                if (lastPoint && lastPoint.x !== undefined) {
                    currentDataTimeHours = lastPoint.x
                }
            }
        }

        // 获取当前视图的显示范围
        var displayRangeHours = 24.0
        switch(smokeChart.currentZoomIndex) {
            case 1: displayRangeHours = 12.0; break  // 12小时视图
            case 2: displayRangeHours = 8.0; break   // 8小时视图
            case 3: displayRangeHours = 1.0; break   // 1小时视图
        }

        // 如果当前数据时间超过显示范围，则需要滚动条
        // 例如：8小时视图，如果数据已经采集了10小时，就需要滚动条
        if (currentDataTimeHours > displayRangeHours) {
            smokeChart.needsScrollbar = true
            var newMaxScrollOffset = currentDataTimeHours - displayRangeHours
            smokeChart.maxScrollOffset = newMaxScrollOffset

            // 如果用户没有手动滚动，自动跟随最新数据
            if (!smokeChart.userScrolling) {
                smokeChart.scrollOffset = smokeChart.maxScrollOffset
            } else {
                // 用户正在手动滚动，确保滚动位置不超出范围
                smokeChart.scrollOffset = Math.min(smokeChart.scrollOffset, smokeChart.maxScrollOffset)
            }
        } else {
            smokeChart.needsScrollbar = false
            smokeChart.maxScrollOffset = 0
            smokeChart.scrollOffset = 0
            smokeChart.userScrolling = false
        }
    }

    function updateChartWithScroll() {
        // 根据当前模式选择不同的滚动处理方式
        if (isShowingHistorical) {
            // 历史数据模式：重新绘制历史数据，应用当前滚动偏移
            updateChartWithHistoricalDataScroll()
        } else {
            // 实时数据模式：根据当前缩放级别和滚动偏移量重新绘制图表
            if (smokeChart.currentZoomIndex === 3) {
                // 1小时视图：暂时还是使用原来的分钟函数，后续可以扩展
                monitorWindow.dataSource.updateSmokeChartSeriesWithMinutes(
                    smokeO2Series, smokeCOSeries
                )
            } else {
                // 其他视图：使用带滚动偏移的函数
                monitorWindow.dataSource.updateSmokeChartSeriesWithScroll(
                    smokeO2Series, smokeCOSeries,
                    smokeChart.currentZoomIndex,
                    smokeChart.scrollOffset
                )
            }
        }
    }

    function updateChartWithHistoricalDataScroll() {
        if (currentHistoricalData.length === 0) {
            return
        }

        // 清空现有图表数据
        smokeO2Series.clear()
        smokeCOSeries.clear()

        var data = currentHistoricalData
        var firstTimestamp = data[0].timestamp
        var lastTimestamp = data[data.length - 1].timestamp
        var totalTimeSeconds = lastTimestamp - firstTimestamp

        // 根据当前缩放级别确定时间单位和显示范围
        var useMinutes = smokeChart.currentZoomIndex === 3
        var displayRange = 24.0
        switch(smokeChart.currentZoomIndex) {
            case 0: displayRange = 24.0; break   // 24小时
            case 1: displayRange = 12.0; break   // 12小时
            case 2: displayRange = 8.0; break    // 8小时
            case 3: displayRange = 60.0; break   // 1小时 = 60分钟
        }

        // 使用当前的滚动偏移量
        var timeOffset = smokeChart.scrollOffset

        // 添加历史数据到图表
        for (var i = 0; i < data.length; i++) {
            var point = data[i]
            var pointTimestamp = point.timestamp

            // 计算相对时间（从第一个数据点开始的时间）
            var relativeTimeSeconds = pointTimestamp - firstTimestamp
            var relativeTime = useMinutes ?
                (relativeTimeSeconds / 60.0) :     // 分钟
                (relativeTimeSeconds / 3600.0)     // 小时

            // 应用滚动偏移量
            var displayTime = relativeTime - timeOffset

            // 只显示在显示范围内的数据点
            if (displayTime >= 0.0 && displayTime <= displayRange) {
                smokeO2Series.append(displayTime, point.o2 || 0)
                smokeCOSeries.append(displayTime, point.co || 0)
            }
        }
    }

    // 将历史数据转换为表格格式
    function convertHistoricalDataToTableFormat(data) {
        if (data.length === 0) {
            historicalTableData = []
            return
        }

        // 创建临时数组来存储转换后的数据
        var tempTableData = []

        // 将历史数据转换为表格格式，显示所有历史数据
        var maxRows = data.length  // 显示所有历史数据
        var startIndex = 0

        // 从最新的数据开始，倒序添加到表格（最新的在顶部）
        for (var i = data.length - 1; i >= startIndex; i--) {
            var point = data[i]
            var row = {
                "time": point.datetime || point.time || "未知时间",
                "o2": (point.o2 || 0).toFixed(3),
                "co": (point.co || 0).toFixed(0),
                "temperature": (point.temperature || 0).toFixed(1),
                "voltage": (point.voltage || 0).toFixed(1),
                "current": (point.current || 0).toFixed(3),
                "switch1": (point.switch1 || 0).toString()
            }
            tempTableData.push(row)
        }

        // 不需要反转，因为我们已经倒序添加了

        // 一次性赋值给属性，触发属性变化通知
        historicalTableData = tempTableData
    }
}
